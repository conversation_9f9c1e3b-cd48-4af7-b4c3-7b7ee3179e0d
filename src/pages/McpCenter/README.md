# KFC MCP中心

## 功能概述

KFC MCP中心是一个专门用于肯德基内部MCP（Model Context Protocol）服务分发和管理的平台，类似于[Glama MCP Servers](https://glama.ai/mcp/servers)，但专注于肯德基内部的服务生态。

## 主要功能

### 🔍 服务浏览与搜索
- **智能搜索**: 支持按服务名称、描述、标签进行模糊搜索
- **分类筛选**: 按功能分类浏览（点餐相关、营销相关、知识服务、便捷功能）
- **实时统计**: 显示总服务数、官方服务数、下载量、平均评分等统计信息

### 📊 服务展示
- **卡片式布局**: 美观的卡片展示，包含服务图标、名称、描述、标签等
- **评分系统**: 5星评分显示服务质量
- **官方标识**: 区分官方服务和第三方服务
- **推荐服务**: 特色服务具有特殊视觉效果
- **详细信息**: 点击查看完整的服务详情

### 💾 服务管理
- **一键安装**: 复制安装命令到剪贴板
- **版本管理**: 显示当前版本和更新时间
- **依赖管理**: 查看API端点和环境变量要求
- **文档链接**: 快速访问服务文档和代码仓库

### 🔧 环境变量管理
- **集中配置**: 统一管理所有MCP服务的环境变量
- **安全输入**: 敏感信息使用密码框输入
- **必需标识**: 清晰标记必需和可选的环境变量
- **描述说明**: 详细的变量用途说明

## 服务分类

### 🍟 点餐相关 (Ordering)
- **KFC智能点餐助手**: AI驱动的智能点餐服务
- **KFC菜单管理服务**: 动态菜单和价格管理

### 📈 营销相关 (Marketing)
- **KFC营销推广引擎**: 智能营销和用户画像分析
- **KFC会员积分系统**: 完整的会员管理体系

### 📚 知识服务 (Knowledge)
- **KFC知识库服务**: 企业知识管理和文档检索

### 🛠️ 便捷功能 (Utility)
- **KFC门店定位服务**: 智能门店查找和导航
- **KFC支付网关**: 统一支付处理服务
- **KFC数据分析面板**: 实时数据分析和可视化

## 技术特性

### 🎨 现代化UI设计
- **渐变背景**: 优雅的渐变色背景
- **悬浮效果**: 卡片悬浮和阴影效果
- **动画交互**: 流畅的动画过渡效果
- **响应式设计**: 适配不同屏幕尺寸

### 🚀 性能优化
- **虚拟滚动**: 大量数据的高效渲染
- **懒加载**: 按需加载服务详情
- **缓存机制**: 智能缓存减少重复请求
- **模拟延迟**: 真实的加载体验

### 🔒 安全特性
- **权限控制**: 基于用户角色的访问控制
- **安全存储**: 环境变量的安全存储和传输
- **输入验证**: 严格的输入数据验证

## 使用指南

### 访问路径
- **菜单导航**: 侧边栏 → MCP中心
- **直接访问**: `/mcp-center`

### 基本操作
1. **浏览服务**: 在主页面查看所有可用的MCP服务
2. **搜索服务**: 使用搜索框或分类标签过滤服务
3. **查看详情**: 点击服务卡片查看详细信息
4. **安装服务**: 点击安装按钮复制安装命令
5. **管理环境**: 使用环境变量管理功能配置服务

### 高级功能
- **批量操作**: 选择多个服务进行批量操作
- **收藏服务**: 收藏常用服务便于快速访问
- **使用统计**: 查看服务使用情况和性能指标

## 开发信息

### 文件结构
```
src/pages/McpCenter/
├── index.tsx           # 主组件文件
├── index.less          # 样式文件
└── README.md          # 功能说明文档
```

### 核心组件
- **McpCenter**: 主页面组件
- **ServerCard**: 服务卡片组件
- **SearchPanel**: 搜索面板组件
- **DetailModal**: 详情弹窗组件
- **EnvDrawer**: 环境变量抽屉组件

### 数据模型
```typescript
interface McpServer {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  // ... 更多字段
}
```

## 未来规划

### 短期目标
- [ ] 集成真实的MCP服务数据
- [ ] 添加服务评论和评分功能
- [ ] 实现服务使用统计和监控
- [ ] 支持服务版本管理和更新通知

### 长期目标
- [ ] 建立MCP服务开发者社区
- [ ] 实现自动化服务测试和部署
- [ ] 支持多环境配置管理
- [ ] 集成AI助手进行服务推荐

## 联系信息

如有问题或建议，请联系开发团队：
- **邮箱**: <EMAIL>
- **文档**: https://docs.kfc.com/mcp/center
- **仓库**: https://github.com/kfc/mcp-center 