import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>ontainer,
  ProCard,
} from '@ant-design/pro-components';
import {
  Input,
  Card,
  Row,
  Col,
  Tag,
  Space,
  Button,
  Modal,
  Drawer,
  Typography,
  Rate,
  Statistic,
  Divider,
  Empty,
  message,
  Tabs,
  List,
  Spin,
} from 'antd';
import {
  SearchOutlined,
  SettingOutlined,
  CloudServerOutlined,
  StarOutlined,
  DownloadOutlined,
  CodeOutlined,
  FireOutlined,
  RocketOutlined,
  CoffeeOutlined,
  BulbOutlined,
  ToolOutlined,
  ApiOutlined,
  KeyOutlined,
  TagOutlined,
} from '@ant-design/icons';
import './index.less';

const { Search } = Input;
const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface McpServer {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  author: string;
  version: string;
  downloads: number;
  stars: number;
  rating: number;
  lastUpdated: string;
  repository: string;
  language: string;
  license: string;
  featured: boolean;
  official: boolean;
  endpoints: string[];
  envKeys: string[];
  installCommand: string;
  documentation: string;
  icon?: string;
}

interface EnvConfig {
  key: string;
  value: string;
  description: string;
  required: boolean;
}

const McpCenter: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [mcpServers, setMcpServers] = useState<McpServer[]>([]);
  const [filteredServers, setFilteredServers] = useState<McpServer[]>([]);
  const [loading, setLoading] = useState(false);
  const [envDrawerVisible, setEnvDrawerVisible] = useState(false);
  const [envConfigs, setEnvConfigs] = useState<EnvConfig[]>([]);
  const [selectedServer, setSelectedServer] = useState<McpServer | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  
  // 热门搜索标签
  const hotSearchTags = [
    '点餐', '菜单', '营销', '推广', '会员', '积分', 
    '知识库', '定位', '支付', '数据分析', '智能', '语音'
  ];

  // 预定义的美观颜色组合
  const tagColors = [
    { bg: '#f6ffed', color: '#52c41a', border: '#b7eb8f' }, // 绿色
    { bg: '#e6f7ff', color: '#1890ff', border: '#91d5ff' }, // 蓝色
    { bg: '#fff2e8', color: '#fa8c16', border: '#ffd591' }, // 橙色
    { bg: '#f9f0ff', color: '#722ed1', border: '#d3adf7' }, // 紫色
    { bg: '#fff1f0', color: '#f5222d', border: '#ffadd2' }, // 红色
    { bg: '#feffe6', color: '#fadb14', border: '#fffb8f' }, // 黄色
    { bg: '#f0f9ff', color: '#13c2c2', border: '#87e8de' }, // 青色
    { bg: '#fff0f6', color: '#eb2f96', border: '#ffadd2' }, // 粉色
    { bg: '#f6f0ff', color: '#9254de', border: '#c89cff' }, // 淡紫色
    { bg: '#e8f4f8', color: '#2f54eb', border: '#adc6ff' }, // 深蓝色
    { bg: '#f2f8f0', color: '#389e0d', border: '#95de64' }, // 深绿色
    { bg: '#fef7e6', color: '#d46b08', border: '#ffc069' }  // 深橙色
  ];

  // 为每个标签生成固定的颜色（基于标签名称的哈希值）
  const getTagColor = (tag: string) => {
    let hash = 0;
    for (let i = 0; i < tag.length; i++) {
      hash = tag.charCodeAt(i) + ((hash << 5) - hash);
    }
    const index = Math.abs(hash) % tagColors.length;
    return tagColors[index];
  };

  // 模拟MCP服务数据
  const mockMcpServers: McpServer[] = [
    {
      id: 'kfc-ordering-ai',
      name: 'KFC智能点餐助手',
      description: '基于AI的智能点餐服务，支持语音点餐、菜品推荐、营养分析等功能',
      category: 'ordering',
      tags: ['AI', 'NLP', '语音识别', '推荐系统'],
      author: 'KFC Tech Team',
      version: '2.1.3',
      downloads: 15420,
      stars: 892,
      rating: 4.8,
      lastUpdated: '2024-01-15',
      repository: 'https://github.com/kfc/ordering-ai',
      language: 'Python',
      license: 'MIT',
      featured: true,
      official: true,
      endpoints: ['/api/order', '/api/recommend', '/api/voice'],
      envKeys: ['KFC_API_KEY', 'AI_MODEL_URL', 'VOICE_SERVICE_KEY'],
      installCommand: 'npm install @kfc/ordering-ai-mcp',
      documentation: 'https://docs.kfc.com/mcp/ordering-ai',
      icon: '🍟'
    },
    {
      id: 'kfc-menu-manager',
      name: 'KFC菜单管理服务',
      description: '动态菜单管理系统，支持实时价格更新、库存管理、促销活动配置',
      category: 'ordering',
      tags: ['菜单管理', '价格更新', '库存', '促销'],
      author: 'KFC Operations',
      version: '1.8.7',
      downloads: 8932,
      stars: 567,
      rating: 4.6,
      lastUpdated: '2024-01-12',
      repository: 'https://github.com/kfc/menu-manager',
      language: 'TypeScript',
      license: 'Apache 2.0',
      featured: false,
      official: true,
      endpoints: ['/api/menu', '/api/pricing', '/api/inventory'],
      envKeys: ['MENU_DB_URL', 'PRICING_API_KEY'],
      installCommand: 'npm install @kfc/menu-manager-mcp',
      documentation: 'https://docs.kfc.com/mcp/menu-manager'
    },
    {
      id: 'kfc-marketing-engine',
      name: 'KFC营销推广引擎',
      description: '智能营销系统，包含用户画像分析、精准推送、A/B测试、转化追踪等功能',
      category: 'marketing',
      tags: ['营销自动化', '用户画像', 'A/B测试', '数据分析'],
      author: 'KFC Marketing',
      version: '3.2.1',
      downloads: 12658,
      stars: 743,
      rating: 4.7,
      lastUpdated: '2024-01-14',
      repository: 'https://github.com/kfc/marketing-engine',
      language: 'Python',
      license: 'MIT',
      featured: true,
      official: true,
      endpoints: ['/api/campaigns', '/api/analytics', '/api/targeting'],
      envKeys: ['MARKETING_DB_URL', 'ANALYTICS_KEY', 'CAMPAIGN_API_KEY'],
      installCommand: 'pip install kfc-marketing-engine-mcp',
      documentation: 'https://docs.kfc.com/mcp/marketing-engine',
      icon: '📈'
    },
    {
      id: 'kfc-loyalty-system',
      name: 'KFC会员积分系统',
      description: '完整的会员积分管理系统，支持积分获取、兑换、等级管理、专属优惠',
      category: 'marketing',
      tags: ['会员管理', '积分系统', '等级权益', '优惠券'],
      author: 'KFC CRM Team',
      version: '2.5.4',
      downloads: 9876,
      stars: 623,
      rating: 4.5,
      lastUpdated: '2024-01-10',
      repository: 'https://github.com/kfc/loyalty-system',
      language: 'Java',
      license: 'Apache 2.0',
      featured: false,
      official: true,
      endpoints: ['/api/points', '/api/rewards', '/api/membership'],
      envKeys: ['LOYALTY_DB_URL', 'POINTS_API_KEY'],
      installCommand: 'mvn install kfc-loyalty-system-mcp',
      documentation: 'https://docs.kfc.com/mcp/loyalty-system'
    },
    {
      id: 'kfc-knowledge-base',
      name: 'KFC知识库服务',
      description: '企业知识管理系统，包含产品知识、培训资料、常见问题、操作手册等',
      category: 'knowledge',
      tags: ['知识管理', '文档检索', '培训资料', 'FAQ'],
      author: 'KFC Knowledge Team',
      version: '1.9.2',
      downloads: 6754,
      stars: 445,
      rating: 4.4,
      lastUpdated: '2024-01-08',
      repository: 'https://github.com/kfc/knowledge-base',
      language: 'TypeScript',
      license: 'MIT',
      featured: false,
      official: true,
      endpoints: ['/api/search', '/api/documents', '/api/faq'],
      envKeys: ['KB_DB_URL', 'SEARCH_API_KEY'],
      installCommand: 'npm install @kfc/knowledge-base-mcp',
      documentation: 'https://docs.kfc.com/mcp/knowledge-base',
      icon: '📚'
    },
    {
      id: 'kfc-store-locator',
      name: 'KFC门店定位服务',
      description: '智能门店定位和导航服务，支持最近门店查找、营业时间查询、路线规划',
      category: 'utility',
      tags: ['地理位置', '门店查找', '导航', '营业时间'],
      author: 'KFC GIS Team',
      version: '2.3.6',
      downloads: 11234,
      stars: 678,
      rating: 4.6,
      lastUpdated: '2024-01-13',
      repository: 'https://github.com/kfc/store-locator',
      language: 'Python',
      license: 'Apache 2.0',
      featured: false,
      official: true,
      endpoints: ['/api/stores', '/api/navigation', '/api/hours'],
      envKeys: ['MAP_API_KEY', 'STORE_DB_URL'],
      installCommand: 'pip install kfc-store-locator-mcp',
      documentation: 'https://docs.kfc.com/mcp/store-locator',
      icon: '📍'
    },
    {
      id: 'kfc-payment-gateway',
      name: 'KFC支付网关',
      description: '统一支付处理服务，支持多种支付方式、订单管理、退款处理、安全验证',
      category: 'utility',
      tags: ['支付处理', '订单管理', '安全', '多渠道'],
      author: 'KFC Payment Team',
      version: '3.1.8',
      downloads: 13567,
      stars: 789,
      rating: 4.9,
      lastUpdated: '2024-01-16',
      repository: 'https://github.com/kfc/payment-gateway',
      language: 'Java',
      license: 'Apache 2.0',
      featured: true,
      official: true,
      endpoints: ['/api/payment', '/api/orders', '/api/refund'],
      envKeys: ['PAYMENT_API_KEY', 'MERCHANT_ID', 'SECURITY_KEY'],
      installCommand: 'mvn install kfc-payment-gateway-mcp',
      documentation: 'https://docs.kfc.com/mcp/payment-gateway',
      icon: '💳'
    },
    {
      id: 'kfc-analytics-dashboard',
      name: 'KFC数据分析面板',
      description: '实时数据分析和可视化平台，提供销售统计、用户行为分析、业务洞察',
      category: 'utility',
      tags: ['数据分析', '可视化', '实时统计', '业务洞察'],
      author: 'KFC Analytics',
      version: '2.7.3',
      downloads: 7892,
      stars: 534,
      rating: 4.5,
      lastUpdated: '2024-01-11',
      repository: 'https://github.com/kfc/analytics-dashboard',
      language: 'TypeScript',
      license: 'MIT',
      featured: false,
      official: true,
      endpoints: ['/api/analytics', '/api/reports', '/api/insights'],
      envKeys: ['ANALYTICS_DB_URL', 'DASHBOARD_API_KEY'],
      installCommand: 'npm install @kfc/analytics-dashboard-mcp',
      documentation: 'https://docs.kfc.com/mcp/analytics-dashboard',
      icon: '📊'
    }
  ];

  const categories = [
    { key: 'all', label: '全部分类', icon: <ApiOutlined />, count: mockMcpServers.length },
    { key: 'ordering', label: '点餐相关', icon: <CoffeeOutlined />, count: 2 },
    { key: 'marketing', label: '营销相关', icon: <FireOutlined />, count: 2 },
    { key: 'knowledge', label: '知识服务', icon: <BulbOutlined />, count: 1 },
    { key: 'utility', label: '便捷功能', icon: <ToolOutlined />, count: 3 },
  ];

  // 初始化数据
  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setMcpServers(mockMcpServers);
      setFilteredServers(mockMcpServers);
      setLoading(false);
    }, 800);
  }, []);

  // 搜索和筛选
  useEffect(() => {
    let filtered = mcpServers;

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(server => server.category === selectedCategory);
    }

    if (searchText) {
      filtered = filtered.filter(server =>
        server.name.toLowerCase().includes(searchText.toLowerCase()) ||
        server.description.toLowerCase().includes(searchText.toLowerCase()) ||
        server.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    setFilteredServers(filtered);
  }, [searchText, selectedCategory, mcpServers]);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'ordering': return <CoffeeOutlined />;
      case 'marketing': return <FireOutlined />;
      case 'knowledge': return <BulbOutlined />;
      case 'utility': return <ToolOutlined />;
      default: return <ApiOutlined />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'ordering': return '#f56a00';
      case 'marketing': return '#cf1322';
      case 'knowledge': return '#1890ff';
      case 'utility': return '#52c41a';
      default: return '#722ed1';
    }
  };

  const handleServerClick = (server: McpServer) => {
    setSelectedServer(server);
    setDetailModalVisible(true);
  };

  const handleInstall = (server: McpServer) => {
    message.success(`${server.name} 安装命令已复制到剪贴板`);
    navigator.clipboard.writeText(server.installCommand);
  };

  const handleTagClick = (tag: string) => {
    setSearchText(tag);
  };

  const showEnvManager = () => {
    setEnvDrawerVisible(true);
    // 模拟环境变量配置
    setEnvConfigs([
      { key: 'KFC_API_KEY', value: '****-****-****-1234', description: 'KFC API 访问密钥', required: true },
      { key: 'AI_MODEL_URL', value: 'https://ai.kfc.com/v1', description: 'AI模型服务地址', required: true },
      { key: 'VOICE_SERVICE_KEY', value: '****-****-****-5678', description: '语音服务密钥', required: false },
      { key: 'MARKETING_DB_URL', value: 'postgresql://****', description: '营销数据库连接', required: true },
    ]);
  };

  if (loading) {
    return (
      <PageContainer
        title="KFC MCP中心"
        subTitle="肯德基模型上下文协议服务分发平台"
        className="mcp-center-container"
      >
        <div className="loading-spinner">
          <Spin size="large" tip="正在加载MCP服务..." />
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer
      title="KFC MCP中心"
      subTitle="肯德基模型上下文协议服务分发平台"
      extra={[
        <Button
          key="env"
          icon={<KeyOutlined />}
          onClick={showEnvManager}
        >
          环境变量管理
        </Button>
      ]}
      className="mcp-center-container"
    >
      {/* 统计面板 */}
      <ProCard className="stats-panel" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="总服务数"
              value={mcpServers.length}
              prefix={<CloudServerOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="官方服务"
              value={mcpServers.filter(s => s.official).length}
              prefix={<RocketOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="总下载量"
              value={mcpServers.reduce((sum, s) => sum + s.downloads, 0)}
              prefix={<DownloadOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="平均评分"
              value={4.6}
              precision={1}
              prefix={<StarOutlined />}
              suffix="/ 5.0"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Col>
        </Row>
      </ProCard>

      {/* 搜索面板 */}
      <ProCard className="search-panel" style={{ marginBottom: 16 }}>
        <Row gutter={24} align="middle">
          <Col xs={24} sm={24} md={12} lg={12}>
            <div className="search-box">
              <Search
                placeholder="搜索MCP服务..."
                size="large"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
                enterButton={<SearchOutlined />}
                className="enhanced-search"
              />
            </div>
          </Col>
          <Col xs={24} sm={24} md={12} lg={12}>
            <div className="tag-wall">
              <div className="tag-wall-title">
                <TagOutlined /> 热门搜索
              </div>
              <div className="tag-wall-content">
                <Space wrap size={[8, 8]}>
                  {hotSearchTags.map(tag => {
                    const tagColor = getTagColor(tag);
                    return (
                      <Tag
                        key={tag}
                        className="hot-search-tag"
                        onClick={() => handleTagClick(tag)}
                        style={{
                          backgroundColor: tagColor.bg,
                          color: tagColor.color,
                          borderColor: tagColor.border,
                          '--hover-bg': tagColor.color,
                          '--hover-color': 'white'
                        } as React.CSSProperties}
                      >
                        {tag}
                      </Tag>
                    );
                  })}
                </Space>
              </div>
            </div>
          </Col>
        </Row>
      </ProCard>

      {/* 分类面板 */}
      <ProCard className="category-panel" style={{ marginBottom: 24 }}>
        <Row justify="center">
          <Col span={24}>
            <div className="category-container">
              <Space wrap size={[12, 12]} style={{ justifyContent: 'center', width: '100%' }}>
                {categories.map(category => (
                  <Tag.CheckableTag
                    key={category.key}
                    checked={selectedCategory === category.key}
                    onChange={() => setSelectedCategory(category.key)}
                    className="category-tag"
                  >
                    {category.icon}
                    <span style={{ marginLeft: 6 }}>
                      {category.label} ({category.count})
                    </span>
                  </Tag.CheckableTag>
                ))}
              </Space>
            </div>
          </Col>
        </Row>
      </ProCard>

      {/* MCP服务列表 */}
      <Row gutter={[24, 24]}>
        {filteredServers.length === 0 ? (
          <Col span={24}>
            <Empty description="暂无匹配的MCP服务" />
          </Col>
        ) : (
          filteredServers.map(server => (
            <Col key={server.id} xs={24} sm={12} lg={8} xl={6}>
              <div 
                className="modern-mcp-card" 
                data-category={server.category}
                onClick={() => handleServerClick(server)}
              >
                {/* 卡片头部 */}
                <div className="card-header">
                  <div className="card-avatar">
                    <div className="avatar-icon">
                      {server.icon || getCategoryIcon(server.category)}
                    </div>
                  </div>
                                     <div className="card-meta">
                     <div className="card-title">
                       {server.name}
                       {server.official && (
                         <span className="official-badge">
                           <RocketOutlined /> 官方
                         </span>
                       )}
                     </div>
                   </div>
                  <div className="card-rating">
                    <div 
                      className="rating-badge"
                      data-rating={server.rating >= 4.5 ? 'A' : server.rating >= 4.0 ? 'B' : 'C'}
                    >
                      {server.rating >= 4.5 ? 'A' : server.rating >= 4.0 ? 'B' : 'C'}
                      <span className="rating-label">
                        {server.rating >= 4.5 ? '优质' : server.rating >= 4.0 ? '良好' : '一般'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* 卡片内容 */}
                <div className="card-content">
                  <div className="card-description">
                    {server.description}
                  </div>
                  
                  <div className="card-tags">
                    {server.tags.slice(0, 3).map(tag => {
                      const tagColor = getTagColor(tag);
                      return (
                        <span
                          key={tag}
                          className="modern-tag"
                          style={{
                            backgroundColor: tagColor.bg,
                            color: tagColor.color,
                            borderColor: tagColor.border,
                          }}
                        >
                          {tag}
                        </span>
                      );
                    })}
                    {server.tags.length > 3 && (
                      <span className="more-tags">+{server.tags.length - 3}</span>
                    )}
                  </div>
                </div>

                {/* 卡片底部 */}
                <div className="card-footer">
                  <div className="card-stats">
                    <div className="stat-item">
                      <span className="stat-icon">📦</span>
                      <span className="stat-value">{server.downloads.toLocaleString()}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-icon">⭐</span>
                      <span className="stat-value">{server.stars}</span>
                    </div>
                    <div className="stat-item version">
                      <span className="stat-value">v{server.version}</span>
                    </div>
                  </div>
                </div>

                {/* 特色标记 */}
                {server.featured && (
                  <div className="featured-ribbon">
                    <FireOutlined /> 推荐
                  </div>
                )}
              </div>
            </Col>
          ))
        )}
      </Row>

      {/* 服务详情弹窗 */}
      <Modal
        title={
          <div>
            <Space>
              {selectedServer?.icon || getCategoryIcon(selectedServer?.category || '')}
              <span>{selectedServer?.name}</span>
              {selectedServer?.official && (
                <Tag color="blue"><RocketOutlined /> 官方</Tag>
              )}
            </Space>
          </div>
        }
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
          <Button
            key="install"
            type="primary"
            icon={<DownloadOutlined />}
            onClick={() => selectedServer && handleInstall(selectedServer)}
          >
            安装服务
          </Button>,
        ]}
      >
        {selectedServer && (
          <Tabs defaultActiveKey="overview">
            <TabPane tab="概览" key="overview">
              <Row gutter={16}>
                <Col span={16}>
                  <Paragraph>{selectedServer.description}</Paragraph>
                  <div style={{ marginBottom: 16 }}>
                    <Title level={5}>功能特点</Title>
                    <Space wrap>
                      {selectedServer.tags.map(tag => (
                        <Tag key={tag} color={getCategoryColor(selectedServer.category)}>
                          {tag}
                        </Tag>
                      ))}
                    </Space>
                  </div>
                  <div style={{ marginBottom: 16 }}>
                    <Title level={5}>API端点</Title>
                    <List
                      size="small"
                      dataSource={selectedServer.endpoints}
                      renderItem={endpoint => (
                        <List.Item>
                          <CodeOutlined /> <Text code>{endpoint}</Text>
                        </List.Item>
                      )}
                    />
                  </div>
                </Col>
                <Col span={8}>
                  <Card size="small">
                    <Statistic title="下载量" value={selectedServer.downloads} />
                    <Divider />
                    <Statistic title="GitHub Stars" value={selectedServer.stars} />
                    <Divider />
                    <div>
                      <Text strong>评分: </Text>
                      <Rate disabled value={selectedServer.rating} style={{ fontSize: 14 }} />
                      <span style={{ marginLeft: 8 }}>{selectedServer.rating}/5.0</span>
                    </div>
                    <Divider />
                    <div>
                      <Text strong>版本: </Text>v{selectedServer.version}
                    </div>
                    <div>
                      <Text strong>语言: </Text>{selectedServer.language}
                    </div>
                    <div>
                      <Text strong>许可: </Text>{selectedServer.license}
                    </div>
                    <div>
                      <Text strong>更新: </Text>{selectedServer.lastUpdated}
                    </div>
                  </Card>
                </Col>
              </Row>
            </TabPane>
            <TabPane tab="安装配置" key="install">
              <div style={{ marginBottom: 16 }}>
                <Title level={5}>安装命令</Title>
                <Input.TextArea
                  value={selectedServer.installCommand}
                  readOnly
                  autoSize={{ minRows: 2, maxRows: 4 }}
                  style={{ fontFamily: 'monospace' }}
                />
              </div>
              <div>
                <Title level={5}>环境变量配置</Title>
                <List
                  dataSource={selectedServer.envKeys}
                  renderItem={envKey => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={<KeyOutlined />}
                        title={<Text code>{envKey}</Text>}
                        description="请在环境变量管理中配置此项"
                      />
                    </List.Item>
                  )}
                />
              </div>
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* 环境变量管理抽屉 */}
      <Drawer
        title="环境变量管理"
        width={600}
        open={envDrawerVisible}
        onClose={() => setEnvDrawerVisible(false)}
        extra={
          <Button type="primary" icon={<SettingOutlined />}>
            保存配置
          </Button>
        }
      >
        <List
          dataSource={envConfigs}
          renderItem={config => (
            <List.Item>
              <List.Item.Meta
                avatar={<KeyOutlined style={{ color: config.required ? '#f5222d' : '#52c41a' }} />}
                title={
                  <div>
                    <Space>
                      <Text strong>{config.key}</Text>
                      {config.required && <Tag color="red">必需</Tag>}
                    </Space>
                  </div>
                }
                description={
                  <div>
                    <Paragraph style={{ marginBottom: 8 }}>{config.description}</Paragraph>
                    <Input.Password
                      value={config.value}
                      onChange={(e) => {
                        const newConfigs = envConfigs.map(c =>
                          c.key === config.key ? { ...c, value: e.target.value } : c
                        );
                        setEnvConfigs(newConfigs);
                      }}
                      placeholder="请输入配置值"
                    />
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Drawer>
    </PageContainer>
  );
};

export default McpCenter;
