.mcp-center-container {
  background: #f5f7fa;
  min-height: calc(100vh - 200px);
  padding: 24px;
  max-width: 1700px;
  margin: 0 auto;
  width: 100%;
}
.mcp-center-container .stats-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}
.mcp-center-container .stats-panel:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}
.mcp-center-container .stats-panel .ant-statistic {
  text-align: center;
}
.mcp-center-container .stats-panel .ant-statistic .ant-statistic-title {
  font-weight: 500;
  color: #666;
  margin-bottom: 8px;
  font-size: 14px;
}
.mcp-center-container .stats-panel .ant-statistic .ant-statistic-content {
  font-size: 28px;
  font-weight: 600;
}
.mcp-center-container .search-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
  padding: 24px;
}
.mcp-center-container .search-panel:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}
.mcp-center-container .search-panel .search-box .enhanced-search .ant-input-search.ant-input-search-large .ant-input-group {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(24, 144, 255, 0.1);
  border: 2px solid #f0f2ff;
  transition: all 0.3s ease;
}
.mcp-center-container .search-panel .search-box .enhanced-search .ant-input-search.ant-input-search-large .ant-input-group:hover {
  border-color: #b7d4ff;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
}
.mcp-center-container .search-panel .search-box .enhanced-search .ant-input-search.ant-input-search-large .ant-input-group:focus-within {
  border-color: #1890ff;
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.2);
}
.mcp-center-container .search-panel .search-box .enhanced-search .ant-input-search.ant-input-search-large .ant-input {
  border: none;
  font-size: 16px;
  padding: 12px 16px;
  background: #fafbff;
}
.mcp-center-container .search-panel .search-box .enhanced-search .ant-input-search.ant-input-search-large .ant-input:focus {
  box-shadow: none;
  background: white;
}
.mcp-center-container .search-panel .search-box .enhanced-search .ant-input-search.ant-input-search-large .ant-input::placeholder {
  color: #bfbfbf;
  font-style: italic;
}
.mcp-center-container .search-panel .search-box .enhanced-search .ant-input-search.ant-input-search-large .ant-input-group-addon {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
}
.mcp-center-container .search-panel .search-box .enhanced-search .ant-input-search.ant-input-search-large .ant-input-group-addon .ant-input-search-button {
  background: transparent;
  border: none;
  color: white;
  font-size: 18px;
  height: 48px;
  width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.mcp-center-container .search-panel .search-box .enhanced-search .ant-input-search.ant-input-search-large .ant-input-group-addon .ant-input-search-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}
.mcp-center-container .search-panel .tag-wall .tag-wall-title {
  font-size: 14px;
  font-weight: 600;
  color: #595959;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.mcp-center-container .search-panel .tag-wall .tag-wall-title .anticon {
  color: #1890ff;
}
.mcp-center-container .search-panel .tag-wall .tag-wall-content .hot-search-tag {
  padding: 6px 12px;
  border-radius: 16px;
  border: 1px solid;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}
.mcp-center-container .search-panel .tag-wall .tag-wall-content .hot-search-tag:hover {
  background: var(--hover-bg) !important;
  color: var(--hover-color) !important;
  border-color: var(--hover-bg) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
.mcp-center-container .search-panel .tag-wall .tag-wall-content .hot-search-tag:active {
  transform: translateY(0);
}
.mcp-center-container .category-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
  padding: 20px;
}
.mcp-center-container .category-panel:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}
.mcp-center-container .category-panel .category-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 12px;
}
.mcp-center-container .category-panel .category-tag {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  background: white;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}
.mcp-center-container .category-panel .category-tag:hover {
  border-color: #1890ff;
  color: #1890ff;
}
.mcp-center-container .category-panel .category-tag.ant-tag-checkable-checked {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}
.mcp-center-container .category-panel .category-tag.ant-tag-checkable-checked:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}
.mcp-center-container .mcp-server-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e8e8e8;
  background: white;
  height: 100%;
}
.mcp-center-container .mcp-server-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #1890ff;
}
.mcp-center-container .mcp-server-card .card-cover {
  position: relative;
  height: 120px;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.mcp-center-container .mcp-server-card .card-cover .server-icon {
  font-size: 48px;
  color: white;
}
.mcp-center-container .mcp-server-card .card-cover .card-badges {
  position: absolute;
  top: 12px;
  right: 12px;
}
.mcp-center-container .mcp-server-card .card-cover .card-badges .official-tag {
  margin-bottom: 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}
.mcp-center-container .mcp-server-card .ant-card-body {
  padding: 20px;
}
.mcp-center-container .mcp-server-card .ant-card-meta-title {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}
.mcp-center-container .mcp-server-card .ant-card-meta-description {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}
.mcp-center-container .mcp-server-card .ant-card-actions {
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}
.mcp-center-container .mcp-server-card .ant-card-actions li {
  margin: 0;
  transition: all 0.3s ease;
}
.mcp-center-container .mcp-server-card .ant-card-actions li:hover {
  background: #e6f7ff;
}
.mcp-center-container .mcp-server-card .ant-card-actions li:hover .anticon {
  color: #1890ff;
  transform: scale(1.1);
}
.mcp-center-container .mcp-server-card .ant-card-actions li .anticon {
  font-size: 18px;
  transition: all 0.3s ease;
  color: #666;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}
.mcp-center-container .mcp-server-card.featured {
  border: 2px solid #ffd700;
}
.mcp-center-container .mcp-server-card.featured:hover {
  border-color: #ffb800;
}
.mcp-center-container .ant-modal .ant-modal-header {
  background: #1890ff;
  border-bottom: none;
  border-radius: 8px 8px 0 0;
}
.mcp-center-container .ant-modal .ant-modal-header .ant-modal-title {
  color: white;
  font-weight: 600;
}
.mcp-center-container .ant-modal .ant-modal-close {
  color: white;
}
.mcp-center-container .ant-modal .ant-modal-close:hover {
  color: rgba(255, 255, 255, 0.8);
}
.mcp-center-container .ant-modal .ant-tabs-nav {
  margin: 0;
}
.mcp-center-container .ant-modal .ant-tabs-nav .ant-tabs-tab {
  padding: 12px 24px;
  font-weight: 500;
}
.mcp-center-container .ant-modal .ant-tabs-nav .ant-tabs-tab.ant-tabs-tab-active {
  background: #1890ff;
  color: white;
  border-radius: 8px 8px 0 0;
}
.mcp-center-container .ant-drawer .ant-drawer-header {
  background: #1890ff;
  border-bottom: none;
}
.mcp-center-container .ant-drawer .ant-drawer-header .ant-drawer-title {
  color: white;
  font-weight: 600;
}
.mcp-center-container .ant-drawer .ant-drawer-close {
  color: white;
}
.mcp-center-container .ant-drawer .ant-drawer-close:hover {
  color: rgba(255, 255, 255, 0.8);
}
.mcp-center-container .ant-drawer .ant-drawer-extra .ant-btn-primary {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}
.mcp-center-container .ant-drawer .ant-drawer-extra .ant-btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}
.mcp-center-container .env-config-item {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}
.mcp-center-container .env-config-item:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
}
.mcp-center-container .env-config-item .ant-input-password {
  margin-top: 8px;
}
@media (max-width: 768px) {
  .mcp-center-container {
    padding: 16px;
  }
  .mcp-center-container .stats-panel .ant-statistic {
    margin-bottom: 16px;
  }
  .mcp-center-container .search-panel {
    padding: 16px;
  }
  .mcp-center-container .search-panel .search-box {
    margin-bottom: 20px;
  }
  .mcp-center-container .search-panel .search-box .enhanced-search .ant-input-search.ant-input-search-large .ant-input {
    font-size: 14px;
    padding: 10px 12px;
  }
  .mcp-center-container .search-panel .search-box .enhanced-search .ant-input-search.ant-input-search-large .ant-input-group-addon .ant-input-search-button {
    height: 40px;
    width: 50px;
    font-size: 16px;
  }
  .mcp-center-container .search-panel .tag-wall .tag-wall-title {
    font-size: 13px;
    margin-bottom: 10px;
  }
  .mcp-center-container .search-panel .tag-wall .tag-wall-content .hot-search-tag {
    padding: 4px 8px;
    font-size: 11px;
  }
  .mcp-center-container .mcp-server-card {
    margin-bottom: 16px;
  }
}
.mcp-center-container .loading-spinner {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
  margin: 24px 0;
}
.mcp-center-container .loading-spinner .ant-spin .ant-spin-dot {
  font-size: 24px;
}
.mcp-center-container .loading-spinner .ant-spin .ant-spin-dot i {
  background-color: #1890ff;
}
.mcp-center-container .loading-spinner .ant-spin-text {
  color: #666;
  font-weight: 500;
  margin-top: 16px;
  font-size: 14px;
}
.mcp-center-container .ant-empty {
  margin: 40px 0;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
}
.mcp-center-container .ant-empty .ant-empty-image {
  height: 120px;
  opacity: 0.6;
}
.mcp-center-container .ant-empty .ant-empty-description {
  color: #999;
  font-size: 16px;
  font-weight: 400;
}
.mcp-center-container .modern-mcp-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  padding: 36px 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.mcp-center-container .modern-mcp-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border-color: #e6f7ff;
}
.mcp-center-container .modern-mcp-card .card-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}
.mcp-center-container .modern-mcp-card .card-header .card-avatar {
  flex-shrink: 0;
}
.mcp-center-container .modern-mcp-card .card-header .card-avatar .avatar-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}
.mcp-center-container .modern-mcp-card .card-header .card-meta {
  flex: 1;
  min-width: 0;
}
.mcp-center-container .modern-mcp-card .card-header .card-meta .card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
  margin-bottom: 4px;
  display: inline-block;
  align-items: center;
  gap: 2px;
  flex-wrap: wrap;
}
.mcp-center-container .modern-mcp-card .card-header .card-meta .card-title .official-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  background: #e6f7ff;
  color: #1890ff;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  margin-left: 6px;
}
.mcp-center-container .modern-mcp-card .card-header .card-meta .card-title .official-badge .anticon {
  font-size: 10px;
}
.mcp-center-container .modern-mcp-card .card-header .card-rating {
  flex-shrink: 0;
}
.mcp-center-container .modern-mcp-card .card-header .card-rating .rating-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  background: #52c41a;
  color: white;
}
.mcp-center-container .modern-mcp-card .card-header .card-rating .rating-badge[data-rating="A"] {
  background: #52c41a;
}
.mcp-center-container .modern-mcp-card .card-header .card-rating .rating-badge[data-rating="B"] {
  background: #faad14;
}
.mcp-center-container .modern-mcp-card .card-header .card-rating .rating-badge[data-rating="C"] {
  background: #ff4d4f;
}
.mcp-center-container .modern-mcp-card .card-header .card-rating .rating-badge .rating-label {
  font-size: 10px;
  font-weight: 500;
}
.mcp-center-container .modern-mcp-card .card-content {
  flex: 1;
  margin-bottom: 16px;
}
.mcp-center-container .modern-mcp-card .card-content .card-description {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.mcp-center-container .modern-mcp-card .card-content .card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}
.mcp-center-container .modern-mcp-card .card-content .card-tags .modern-tag {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid;
  white-space: nowrap;
}
.mcp-center-container .modern-mcp-card .card-content .card-tags .more-tags {
  font-size: 12px;
  color: #999;
  font-weight: 500;
}
.mcp-center-container .modern-mcp-card .card-footer {
  border-top: 1px solid #f5f5f5;
  padding-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.mcp-center-container .modern-mcp-card .card-footer .card-stats {
  display: flex;
  gap: 16px;
  align-items: center;
}
.mcp-center-container .modern-mcp-card .card-footer .card-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}
.mcp-center-container .modern-mcp-card .card-footer .card-stats .stat-item .stat-icon {
  font-size: 14px;
}
.mcp-center-container .modern-mcp-card .card-footer .card-stats .stat-item .stat-value {
  font-weight: 500;
}
.mcp-center-container .modern-mcp-card .card-footer .card-stats .stat-item.version .stat-value {
  background: #f5f5f5;
  color: #666;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}
.mcp-center-container .modern-mcp-card .card-footer .card-actions {
  display: flex;
  gap: 4px;
}
.mcp-center-container .modern-mcp-card .card-footer .card-actions .action-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  color: #666;
  transition: all 0.2s;
}
.mcp-center-container .modern-mcp-card .card-footer .card-actions .action-btn:hover {
  color: #1890ff;
  background: #f0f6ff;
}
.mcp-center-container .modern-mcp-card .card-footer .card-actions .action-btn.install-btn:hover {
  color: #52c41a;
  background: #f6ffed;
}
.mcp-center-container .modern-mcp-card .card-footer .card-actions .action-btn.github-btn:hover {
  color: #333;
  background: #f5f5f5;
}
.mcp-center-container .modern-mcp-card .card-footer .card-actions .action-btn .anticon {
  font-size: 16px;
}
.mcp-center-container .modern-mcp-card .featured-ribbon {
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 50%, #ffb3b3 100%);
  color: white;
  padding: 8px 16px 6px 12px;
  border-radius: 0 0 16px 0;
  font-size: 12px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4), 0 2px 4px rgba(255, 107, 107, 0.3);
  transform-origin: top left;
  transition: all 0.3s ease;
  z-index: 10;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
.mcp-center-container .modern-mcp-card .featured-ribbon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  border-radius: inherit;
  pointer-events: none;
}
.mcp-center-container .modern-mcp-card .featured-ribbon .anticon {
  font-size: 12px;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
  animation: sparkle 2s ease-in-out infinite;
}
.mcp-center-container .modern-mcp-card .featured-ribbon:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(255, 107, 107, 0.5), 0 3px 6px rgba(255, 107, 107, 0.4);
}
@keyframes sparkle {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}
.mcp-center-container .modern-mcp-card[data-category="ordering"] .card-avatar .avatar-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}
.mcp-center-container .modern-mcp-card[data-category="marketing"] .card-avatar .avatar-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 4px 16px rgba(240, 147, 251, 0.3);
}
.mcp-center-container .modern-mcp-card[data-category="analytics"] .card-avatar .avatar-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 4px 16px rgba(79, 172, 254, 0.3);
}
.mcp-center-container .modern-mcp-card[data-category="payment"] .card-avatar .avatar-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  box-shadow: 0 4px 16px rgba(67, 233, 123, 0.3);
}
.mcp-center-container .modern-mcp-card[data-category="other"] .card-avatar .avatar-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  box-shadow: 0 4px 16px rgba(250, 112, 154, 0.3);
}
@media (max-width: 768px) {
  .mcp-center-container .modern-mcp-card .card-header .card-meta .card-title {
    font-size: 15px;
  }
  .mcp-center-container .modern-mcp-card .card-header .card-rating .rating-badge {
    font-size: 11px;
    padding: 3px 6px;
  }
  .mcp-center-container .modern-mcp-card .card-content .card-description {
    font-size: 13px;
  }
  .mcp-center-container .modern-mcp-card .card-footer {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  .mcp-center-container .modern-mcp-card .card-footer .card-actions {
    align-self: flex-end;
  }
}
