.mcp-center-container {
  // 页面容器样式
  background: #f5f7fa;
  min-height: calc(100vh - 200px);
  padding: 24px;
  max-width: 1700px;
  margin: 0 auto;
  width: 100%;

  // 统计面板样式
  .stats-panel {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8e8e8;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }

    .ant-statistic {
      text-align: center;
      
      .ant-statistic-title {
        font-weight: 500;
        color: #666;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .ant-statistic-content {
        font-size: 28px;
        font-weight: 600;
      }
    }
  }

  // 搜索面板样式
  .search-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8e8e8;
    transition: all 0.3s ease;
    padding: 24px;

    &:hover {
      box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }

    .search-box {
      .enhanced-search {
        .ant-input-search {
          &.ant-input-search-large {
            .ant-input-group {
              border-radius: 8px;
              overflow: hidden;
              box-shadow: 0 2px 12px rgba(24, 144, 255, 0.1);
              border: 2px solid #f0f2ff;
              transition: all 0.3s ease;

              &:hover {
                border-color: #b7d4ff;
                box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
              }

              &:focus-within {
                border-color: #1890ff;
                box-shadow: 0 4px 20px rgba(24, 144, 255, 0.2);
              }
            }

            .ant-input {
              border: none;
              font-size: 16px;
              padding: 12px 16px;
              background: #fafbff;
              
              &:focus {
                box-shadow: none;
                background: white;
              }

              &::placeholder {
                color: #bfbfbf;
                font-style: italic;
              }
            }

            .ant-input-group-addon {
              background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
              border: none;
              
              .ant-input-search-button {
                background: transparent;
                border: none;
                color: white;
                font-size: 18px;
                height: 48px;
                width: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                
                &:hover {
                  background: rgba(255, 255, 255, 0.2);
                  transform: scale(1.05);
                }
              }
            }
          }
        }
      }
    }

    .tag-wall {
      .tag-wall-title {
        font-size: 14px;
        font-weight: 600;
        color: #595959;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        gap: 6px;

        .anticon {
          color: #1890ff;
        }
      }

      .tag-wall-content {
        .hot-search-tag {
          padding: 6px 12px;
          border-radius: 16px;
          border: 1px solid;
          font-size: 12px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          user-select: none;

          &:hover {
            background: var(--hover-bg) !important;
            color: var(--hover-color) !important;
            border-color: var(--hover-bg) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }

  // 分类面板样式
  .category-panel {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8e8e8;
    transition: all 0.3s ease;
    padding: 20px;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }

    .category-container {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      gap: 12px;
    }

    .category-tag {
      padding: 8px 16px;
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      background: white;
      transition: all 0.3s ease;
      font-weight: 500;
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      cursor: pointer;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      &.ant-tag-checkable-checked {
        background: #1890ff;
        border-color: #1890ff;
        color: white;

        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
        }
      }
    }
  }

  // MCP服务卡片样式
  .mcp-server-card {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e8e8e8;
    background: white;
    height: 100%;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      border-color: #1890ff;
    }

    .card-cover {
      position: relative;
      height: 120px;
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;

      .server-icon {
        font-size: 48px;
        color: white;
      }

      .card-badges {
        position: absolute;
        top: 12px;
        right: 12px;

        .official-tag {
          margin-bottom: 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }

    .ant-card-body {
      padding: 20px;
    }

    .ant-card-meta-title {
      margin-bottom: 12px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      line-height: 1.4;
    }

    .ant-card-meta-description {
      color: #666;
      line-height: 1.6;
      font-size: 14px;
    }

    .ant-card-actions {
      border-top: 1px solid #f0f0f0;
      background: #fafafa;
      
      li {
        margin: 0;
        transition: all 0.3s ease;
        
        &:hover {
          background: #e6f7ff;
          
          .anticon {
            color: #1890ff;
            transform: scale(1.1);
          }
        }
        
        .anticon {
          font-size: 18px;
          transition: all 0.3s ease;
          color: #666;
        }
      }
    }
  }

  // 简单动画
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // 脉冲动画
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
    }
  }

  // 特色服务卡片
  .mcp-server-card.featured {
    border: 2px solid #ffd700;
    
    &:hover {
      border-color: #ffb800;
    }
  }

  // 模态框样式
  .ant-modal {
    .ant-modal-header {
      background: #1890ff;
      border-bottom: none;
      border-radius: 8px 8px 0 0;
      
      .ant-modal-title {
        color: white;
        font-weight: 600;
      }
    }

    .ant-modal-close {
      color: white;
      
      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-tabs-nav {
      margin: 0;
      
      .ant-tabs-tab {
        padding: 12px 24px;
        font-weight: 500;
        
        &.ant-tabs-tab-active {
          background: #1890ff;
          color: white;
          border-radius: 8px 8px 0 0;
        }
      }
    }
  }

  // 抽屉样式
  .ant-drawer {
    .ant-drawer-header {
      background: #1890ff;
      border-bottom: none;
      
      .ant-drawer-title {
        color: white;
        font-weight: 600;
      }
    }

    .ant-drawer-close {
      color: white;
      
      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .ant-drawer-extra {
      .ant-btn-primary {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
        
        &:hover {
          background: rgba(255, 255, 255, 0.3);
          border-color: rgba(255, 255, 255, 0.4);
        }
      }
    }
  }

  // 环境变量列表样式
  .env-config-item {
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    margin-bottom: 16px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
    }

    .ant-input-password {
      margin-top: 8px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;
    
    .stats-panel {
      .ant-statistic {
        margin-bottom: 16px;
      }
    }

    .search-panel {
      padding: 16px;
      
      .search-box {
        margin-bottom: 20px;
        
        .enhanced-search {
          .ant-input-search {
            &.ant-input-search-large {
              .ant-input {
                font-size: 14px;
                padding: 10px 12px;
              }

              .ant-input-group-addon {
                .ant-input-search-button {
                  height: 40px;
                  width: 50px;
                  font-size: 16px;
                }
              }
            }
          }
        }
      }
      
      .tag-wall {
        .tag-wall-title {
          font-size: 13px;
          margin-bottom: 10px;
        }
        
        .tag-wall-content {
          .hot-search-tag {
            padding: 4px 8px;
            font-size: 11px;
          }
        }
      }
    }
    
    .mcp-server-card {
      margin-bottom: 16px;
    }
  }

  // 加载动画
  .loading-spinner {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 200px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8e8e8;
    margin: 24px 0;
    
    .ant-spin {
      .ant-spin-dot {
        font-size: 24px;
        
        i {
          background-color: #1890ff;
        }
      }
    }

    .ant-spin-text {
      color: #666;
      font-weight: 500;
      margin-top: 16px;
      font-size: 14px;
    }
  }

  // 空状态样式
  .ant-empty {
    margin: 40px 0;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8e8e8;
    
    .ant-empty-image {
      height: 120px;
      opacity: 0.6;
    }
    
    .ant-empty-description {
      color: #999;
      font-size: 16px;
      font-weight: 400;
    }
  }

  // 现代化MCP服务卡片样式
  .modern-mcp-card {
    background: white;
    border-radius: 12px;
    border: 1px solid #f0f0f0;
    padding: 36px 20px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      border-color: #e6f7ff;
    }

    // 卡片头部
    .card-header {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 16px;

      .card-avatar {
        flex-shrink: 0;

        .avatar-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: white;
          box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
        }
      }

      .card-meta {
        flex: 1;
        min-width: 0;

        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: #1a1a1a;
          line-height: 1.4;
          margin-bottom: 4px;
          display: inline-block;
          align-items: center;
          gap: 2px;
          flex-wrap: wrap;

          .official-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 2px 8px;
            background: #e6f7ff;
            color: #1890ff;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            margin-left: 6px;

            .anticon {
              font-size: 10px;
            }
          }
        }
      }

      .card-rating {
        flex-shrink: 0;

        .rating-badge {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          border-radius: 8px;
          font-size: 12px;
          font-weight: 600;
          background: #52c41a;
          color: white;

          &[data-rating="A"] {
            background: #52c41a;
          }

          &[data-rating="B"] {
            background: #faad14;
          }

          &[data-rating="C"] {
            background: #ff4d4f;
          }

          .rating-label {
            font-size: 10px;
            font-weight: 500;
          }
        }
      }
    }

    // 卡片内容
    .card-content {
      flex: 1;
      margin-bottom: 16px;

      .card-description {
        font-size: 14px;
        color: #666;
        line-height: 1.6;
        margin-bottom: 16px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .card-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;

        .modern-tag {
          padding: 4px 8px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 500;
          border: 1px solid;
          white-space: nowrap;
        }

        .more-tags {
          font-size: 12px;
          color: #999;
          font-weight: 500;
        }
      }
    }

    // 卡片底部
    .card-footer {
      border-top: 1px solid #f5f5f5;
      padding-top: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-stats {
        display: flex;
        gap: 16px;
        align-items: center;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #666;

          .stat-icon {
            font-size: 14px;
          }

          .stat-value {
            font-weight: 500;
          }

          &.version {
            .stat-value {
              background: #f5f5f5;
              color: #666;
              padding: 2px 6px;
              border-radius: 4px;
              font-size: 11px;
            }
          }
        }
      }

      .card-actions {
        display: flex;
        gap: 4px;

        .action-btn {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 8px;
          color: #666;
          transition: all 0.2s;

          &:hover {
            color: #1890ff;
            background: #f0f6ff;
          }

          &.install-btn:hover {
            color: #52c41a;
            background: #f6ffed;
          }

          &.github-btn:hover {
            color: #333;
            background: #f5f5f5;
          }

          .anticon {
            font-size: 16px;
          }
        }
      }
    }

    // 特色标记 - 左上角推荐标签
    .featured-ribbon {
      position: absolute;
      top: 0;
      left: 0;
      background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 50%, #ffb3b3 100%);
      color: white;
      padding: 8px 16px 6px 12px;
      border-radius: 0 0 16px 0;
      font-size: 12px;
      font-weight: 700;
      display: flex;
      align-items: center;
      gap: 6px;
      box-shadow: 
        0 4px 12px rgba(255, 107, 107, 0.4),
        0 2px 4px rgba(255, 107, 107, 0.3);
      transform-origin: top left;
      transition: all 0.3s ease;
      z-index: 10;
      letter-spacing: 0.5px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
        border-radius: inherit;
        pointer-events: none;
      }

      .anticon {
        font-size: 12px;
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
        animation: sparkle 2s ease-in-out infinite;
      }

      // 悬停效果
      &:hover {
        transform: scale(1.05);
        box-shadow: 
          0 6px 16px rgba(255, 107, 107, 0.5),
          0 3px 6px rgba(255, 107, 107, 0.4);
      }
    }

    // 闪烁动画
    @keyframes sparkle {
      0%, 100% { 
        opacity: 1; 
        transform: scale(1);
      }
      50% { 
        opacity: 0.8; 
        transform: scale(1.1);
      }
    }

    // 不同评级的头像渐变色
    &[data-category="ordering"] .card-avatar .avatar-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    }

    &[data-category="marketing"] .card-avatar .avatar-icon {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      box-shadow: 0 4px 16px rgba(240, 147, 251, 0.3);
    }

    &[data-category="analytics"] .card-avatar .avatar-icon {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      box-shadow: 0 4px 16px rgba(79, 172, 254, 0.3);
    }

    &[data-category="payment"] .card-avatar .avatar-icon {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      box-shadow: 0 4px 16px rgba(67, 233, 123, 0.3);
    }

    &[data-category="other"] .card-avatar .avatar-icon {
      background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      box-shadow: 0 4px 16px rgba(250, 112, 154, 0.3);
    }
  }

  // 响应式优化
  @media (max-width: 768px) {
    .modern-mcp-card {
      .card-header {
        .card-meta .card-title {
          font-size: 15px;
        }

        .card-rating .rating-badge {
          font-size: 11px;
          padding: 3px 6px;
        }
      }

      .card-content .card-description {
        font-size: 13px;
      }

      .card-footer {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;

        .card-actions {
          align-self: flex-end;
        }
      }
    }
  }
} 