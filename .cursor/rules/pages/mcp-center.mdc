---
description: 
globs: 
alwaysApply: false
---
# MCP中心页面 - 功能规范与实现记录

## 页面概述
- **功能定位**: KFC内部模型上下文协议(MCP)服务分发平台
- **访问路径**: `/mcp-center`
- **权限要求**: admin和user角色均可访问
- **主要用户**: 开发人员、系统管理员、技术团队
- **数据源**: 模拟的KFC内部MCP服务数据

## 当前功能特性

### 核心功能
- [x] MCP服务列表展示（8个KFC内部服务）
- [x] 服务分类筛选（点餐相关、营销相关、知识服务、便捷功能）
- [x] 智能搜索功能（支持服务名称、描述、标签搜索）
- [x] 热门搜索标签墙（12个常用标签，随机多彩主题）
- [x] 服务详情弹窗（概览和安装配置）
- [x] 一键安装功能（复制安装命令）
- [x] 环境变量管理（模拟配置管理）
- [x] 统计面板（总服务数、官方服务、下载量、评分）

### 页面布局结构
```
1. 页面标题区域 (PageContainer)
   - 标题：KFC MCP中心
   - 副标题：肯德基模型上下文协议服务分发平台
   - 操作按钮：环境变量管理

2. 统计面板 (ProCard)
   - 总服务数：8个服务
   - 官方服务：8个（100%官方认证）
   - 总下载量：累计下载统计
   - 平均评分：4.6/5.0星

3. 搜索面板 (ProCard) - 美化设计
   - 左侧：增强型搜索框
     * 渐变蓝色边框设计
     * 大号搜索按钮
     * 优雅的focus状态
       - 右侧：标签墙
      * 12个热门搜索标签
      * 随机多彩主题设计（12种颜色组合）
      * 一键快速搜索
      * 基于标签名称的固定颜色分配

4. 分类面板 (ProCard)
   - 全部分类 (8)
   - 点餐相关 (2)：智能点餐助手、菜单管理服务
   - 营销相关 (2)：营销推广引擎、会员积分系统
   - 知识服务 (1)：知识库服务
   - 便捷功能 (3)：门店定位、支付网关、数据分析面板

5. 服务卡片网格 (Row/Col)
   - 响应式布局：xl(6列) lg(8列) sm(12列) xs(24列)
   - 推荐服务特殊边框（金色边框）
   - 官方认证标识
   - 评分、下载量、版本信息
```

### MCP服务数据结构
| 服务ID | 服务名称 | 分类 | 特色 | 下载量 | 评分 |
|--------|---------|------|------|--------|------|
| kfc-ordering-ai | KFC智能点餐助手 | ordering | 推荐+官方 | 15,420 | 4.8 |
| kfc-menu-manager | KFC菜单管理服务 | ordering | 官方 | 8,932 | 4.6 |
| kfc-marketing-engine | KFC营销推广引擎 | marketing | 推荐+官方 | 12,658 | 4.7 |
| kfc-loyalty-system | KFC会员积分系统 | marketing | 官方 | 9,876 | 4.5 |
| kfc-knowledge-base | KFC知识库服务 | knowledge | 官方 | 6,754 | 4.4 |
| kfc-store-locator | KFC门店定位服务 | utility | 官方 | 11,234 | 4.6 |
| kfc-payment-gateway | KFC支付网关 | utility | 推荐+官方 | 13,567 | 4.9 |
| kfc-analytics-dashboard | KFC数据分析面板 | utility | 官方 | 7,892 | 4.5 |

### 搜索与筛选功能
**搜索机制：**
- 服务名称模糊搜索
- 服务描述内容搜索
- 标签关键词匹配
- 实时搜索结果更新

**热门搜索标签：**
```typescript
const hotSearchTags = [
  '点餐', '菜单', '营销', '推广', '会员', '积分', 
  '知识库', '定位', '支付', '数据分析', '智能', '语音'
];

// 12种预定义的美观颜色组合
const tagColors = [
  { bg: '#f6ffed', color: '#52c41a', border: '#b7eb8f' }, // 绿色
  { bg: '#e6f7ff', color: '#1890ff', border: '#91d5ff' }, // 蓝色
  { bg: '#fff2e8', color: '#fa8c16', border: '#ffd591' }, // 橙色
  { bg: '#f9f0ff', color: '#722ed1', border: '#d3adf7' }, // 紫色
  { bg: '#fff1f0', color: '#f5222d', border: '#ffadd2' }, // 红色
  { bg: '#feffe6', color: '#fadb14', border: '#fffb8f' }, // 黄色
  { bg: '#f0f9ff', color: '#13c2c2', border: '#87e8de' }, // 青色
  { bg: '#fff0f6', color: '#eb2f96', border: '#ffadd2' }, // 粉色
  { bg: '#f6f0ff', color: '#9254de', border: '#c89cff' }, // 淡紫色
  { bg: '#e8f4f8', color: '#2f54eb', border: '#adc6ff' }, // 深蓝色
  { bg: '#f2f8f0', color: '#389e0d', border: '#95de64' }, // 深绿色
  { bg: '#fef7e6', color: '#d46b08', border: '#ffc069' }  // 深橙色
];

// 基于标签名称哈希值分配固定颜色
const getTagColor = (tag: string) => {
  let hash = 0;
  for (let i = 0; i < tag.length; i++) {
    hash = tag.charCodeAt(i) + ((hash << 5) - hash);
  }
  const index = Math.abs(hash) % tagColors.length;
  return tagColors[index];
};
```

**分类筛选：**
- 全部分类：显示所有8个服务
- 点餐相关：2个服务（智能点餐、菜单管理）
- 营销相关：2个服务（营销引擎、会员系统）
- 知识服务：1个服务（知识库）
- 便捷功能：3个服务（定位、支付、分析）

### 标签墙颜色机制
**颜色分配策略：**
- 预定义12种精美颜色组合，确保视觉和谐
- 基于标签名称的字符哈希值分配颜色
- 同一标签每次显示相同颜色，不同标签显示不同颜色
- 避免了随机性带来的视觉不一致

**颜色组合包含：**
- 绿色系、蓝色系、橙色系、紫色系
- 红色系、黄色系、青色系、粉色系
- 淡紫色、深蓝色、深绿色、深橙色

**交互效果：**
- Hover时背景变为对应颜色的深色调
- 文字颜色自动变为白色提升对比度
- 保持原有的上浮动画和阴影效果

### 交互功能
**服务卡片操作：**
- 点击卡片：打开服务详情弹窗
- 查看详情：显示服务概览和安装配置
- 一键安装：复制安装命令到剪贴板
- GitHub链接：跳转到代码仓库

**弹窗内容：**
- 概览标签：服务描述、功能特点、技术信息
- 安装配置标签：安装命令、环境变量、API端点

**环境变量管理：**
- 抽屉式侧边栏
- 4个模拟环境变量配置
- 密码输入框和描述信息
- 必需/可选标识

## UI设计风格

### 整体风格：简洁大气
- **背景色**：浅灰色 (#f5f7fa)
- **主题色**：蓝色系 (#1890ff)
- **卡片背景**：纯白色
- **边框圆角**：8px统一设计
- **阴影效果**：subtle box-shadow

### 搜索面板美化设计
```less
// 搜索框增强设计
.enhanced-search {
  .ant-input-search-large {
    .ant-input-group {
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(24, 144, 255, 0.1);
      border: 2px solid #f0f2ff;
    }
    
    .ant-input {
      background: #fafbff;
      font-size: 16px;
      padding: 12px 16px;
    }
    
    .ant-input-group-addon {
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    }
  }
}

// 标签墙设计 - 随机多彩主题
.hot-search-tag {
  padding: 6px 12px;
  border-radius: 16px;
  border: 1px solid;
  // 背景色、文字色、边框色通过内联样式动态设置
  
  &:hover {
    background: var(--hover-bg) !important;
    color: var(--hover-color) !important;
    border-color: var(--hover-bg) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}
```

### 响应式设计
- **桌面端**：搜索栏和标签墙左右并排 (12/12布局)
- **移动端**：搜索栏和标签墙上下排列，标签大小自适应
- **卡片网格**：自动响应式布局，确保不同设备最佳显示效果

## 技术实现架构

### 组件结构
```
McpCenter/
├── index.tsx          # 主组件文件
├── index.less         # 样式文件
└── README.md          # 说明文档
```

### 状态管理
```typescript
// 主要状态变量
const [searchText, setSearchText] = useState('');           // 搜索文本
const [selectedCategory, setSelectedCategory] = useState('all'); // 选中分类
const [mcpServers, setMcpServers] = useState<McpServer[]>([]);   // 服务列表
const [filteredServers, setFilteredServers] = useState<McpServer[]>([]); // 筛选结果
const [selectedServer, setSelectedServer] = useState<McpServer | null>(null); // 选中服务
const [detailModalVisible, setDetailModalVisible] = useState(false); // 详情弹窗
const [envDrawerVisible, setEnvDrawerVisible] = useState(false); // 环境变量抽屉
```

### 核心功能函数
```typescript
// 标签点击处理
const handleTagClick = (tag: string) => {
  setSearchText(tag);
};

// 标签颜色生成（基于哈希值的固定随机颜色）
const getTagColor = (tag: string) => {
  let hash = 0;
  for (let i = 0; i < tag.length; i++) {
    hash = tag.charCodeAt(i) + ((hash << 5) - hash);
  }
  const index = Math.abs(hash) % tagColors.length;
  return tagColors[index];
};

// 服务安装
const handleInstall = (server: McpServer) => {
  message.success(`${server.name} 安装命令已复制到剪贴板`);
  navigator.clipboard.writeText(server.installCommand);
};

// 分类图标获取
const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'ordering': return <CoffeeOutlined />;
    case 'marketing': return <RocketOutlined />;
    case 'knowledge': return <BulbOutlined />;
    case 'utility': return <ToolOutlined />;
    default: return <ApiOutlined />;
  }
};
```

### 数据接口定义
```typescript
interface McpServer {
  id: string;                    // 服务唯一ID
  name: string;                  // 服务名称
  description: string;           // 服务描述
  category: string;              // 服务分类
  tags: string[];               // 标签列表
  author: string;               // 作者
  version: string;              // 版本号
  downloads: number;            // 下载量
  stars: number;                // GitHub星数
  rating: number;               // 评分
  lastUpdated: string;          // 最后更新时间
  repository: string;           // 代码仓库
  language: string;             // 编程语言
  license: string;              // 开源协议
  featured: boolean;            // 是否推荐
  official: boolean;            // 是否官方
  endpoints: string[];          // API端点
  envKeys: string[];           // 环境变量
  installCommand: string;       // 安装命令
  documentation: string;        // 文档链接
  icon?: string;               // 图标
}
```

## 开发历程记录

### 2024-01-16 v1.2.0 现代化卡片重设计
**功能升级**：
- ✅ 重新设计MCP服务卡片，采用现代化布局风格
- ✅ 参照lobehub.com设计理念，使用白色主题
- ✅ 三段式卡片布局：头部（头像+元信息+评级）、内容（描述+标签）、底部（统计+操作）
- ✅ 48px圆角头像图标，配备5种分类渐变色背景和阴影效果
- ✅ 智能评级徽章系统：A级绿色(优质4.5+)、B级橙色(良好4.0+)、C级红色(一般4.0-)

**技术实现**：
- ✅ 替换原有Ant Design Card组件为自定义现代化卡片
- ✅ 使用CSS Grid和Flexbox实现完全响应式布局
- ✅ CSS数据属性(data-category, data-rating)动态样式应用
- ✅ 现代CSS动画：hover悬浮上升4px、渐变阴影、cubic-bezier缓动函数
- ✅ 5种分类渐变色主题：点餐(紫蓝)、营销(粉紫)、分析(蓝青)、支付(绿青)、其他(粉黄)

**视觉设计亮点**：
```css
/* 卡片整体设计 */
.modern-mcp-card {
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }
}

/* 头像渐变色示例 */
&[data-category="ordering"] .avatar-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

/* 评级徽章颜色 */
.rating-badge[data-rating="A"] { background: #52c41a; } /* 绿色-优质 */
.rating-badge[data-rating="B"] { background: #faad14; } /* 橙色-良好 */
.rating-badge[data-rating="C"] { background: #ff4d4f; } /* 红色-一般 */
```

**用户体验改进**：
- ✅ 卡片信息层次更清晰，头像、标题、作者、评级一目了然
- ✅ 操作按钮悬浮效果：查看(蓝色)、安装(绿色)、GitHub(黑色)
- ✅ 特色服务右上角渐变色ribbon标记，视觉更突出
- ✅ 移动端底部操作区垂直布局，触控操作更便捷

**影响范围**：MCP中心页面卡片展示全面现代化升级，样式文件新增200+行CSS规则

### 2024-01-17 初始版本
- ✅ 创建基础页面结构和路由配置
- ✅ 实现8个模拟MCP服务数据
- ✅ 完成服务列表展示和分类筛选
- ✅ 添加搜索功能和统计面板
- ✅ 实现服务详情弹窗和安装功能

### 2024-01-17 UI优化
- ✅ 去除所有水印显示（关闭waterMarkProps）
- ✅ 改为简洁大气的设计风格
- ✅ 优化色彩搭配和组件样式
- ✅ 简化动画效果，保持专业感

### 2024-01-17 搜索体验优化
- ✅ 美化搜索栏设计（渐变边框、大号按钮）
- ✅ 新增标签墙功能（12个热门搜索标签）
- ✅ 搜索栏和标签墙分离布局
- ✅ 实现一键快速搜索功能
- ✅ 完善移动端响应式设计

### 2024-01-17 标签墙颜色优化
- ✅ 标签墙采用随机颜色显示（12种精美颜色组合）
- ✅ 基于标签名称哈希值分配固定颜色
- ✅ 优化hover效果（背景变深色，文字变白）
- ✅ 保持动画效果（上浮、阴影增强）

## 待优化功能
- [ ] 接入真实的MCP服务API
- [ ] 添加服务收藏功能
- [ ] 实现服务评论和评分系统
- [ ] 添加服务使用统计和监控
- [ ] 支持服务版本管理和更新通知
- [ ] 实现服务依赖关系图谱

## 相关文档
- [项目总览](mdc:.cursor/rules/project-overview.mdc)
- [技术架构指南](mdc:.cursor/rules/project-structure.mdc)
- [AI更新指导](mdc:.cursor/rules/ai-updata-guide.mdc)
