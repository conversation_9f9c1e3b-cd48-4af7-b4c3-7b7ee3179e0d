---
description: 
globs: 
alwaysApply: true
---
# KFC智能运营管理平台 - 项目总览

## 项目基本信息
- **项目类型**: 基于Ant Design Pro的企业级管理系统
- **技术栈**: React + TypeScript + UmiJS 4.x + Supabase
- **项目规模**: 6个主要功能页面，支持多角色权限控制
- **官方文档**: https://pro.ant.design/zh-CN/docs/overview
- **数据服务**: Supabase (https://supabase.com/docs/reference/javascript/introduction)

## 核心功能模块
1. **用户管理** - 管理员专用，用户CRUD操作
2. **百度地址管理** - 门店信息维护，KFC API集成
3. **腾讯地址管理** - 腾讯地图服务同步
4. **MCP中心** - KFC内部MCP服务分发平台，服务管理与安装
5. **AI反馈统计** - 点餐反馈数据分析
6. **权限控制** - 基于角色的访问控制

## 开发规范总则
- 功能实现优先使用Ant Design Pro组件，避免自定义组件
- 严格遵循TypeScript类型安全
- API服务统一封装在services层
- 权限控制在路由和组件双重保护
- 国际化支持（8种语言）

## 权限角色定义
- **admin**: 管理员，可访问所有功能
- **user**: 普通用户，可访问AI反馈统计和MCP中心

## ⚠️ AI助手更新机制

**重要提醒：功能完成后必须更新Rules！**

详细的更新指导请参考: [ai-updata-guide.mdc](mdc:.cursor/rules/ai-updata-guide.mdc)

### 快速更新检查清单
- [ ] 新增功能 → 更新对应页面rules
- [ ] 修复问题 → 更新technical-issues.mdc  
- [ ] API变更 → 更新api-services.mdc
- [ ] 组件优化 → 更新components.mdc

## 项目迭代流程
1. 需求分析和技术方案设计
2. 在对应页面rules文件中记录实现计划
3. 开发实现
4. **立即更新页面rules文件记录实际实现**

## Rules文件组织结构
- `project-overview.mdc` - 本文件，项目总览
- `pages/` - 页面级详细规范
- `shared/` - 共享规范和约定
- `history/` - 历史问题和迁移记录

## 相关文档
- [项目结构指南](mdc:.cursor/rules/project-structure.mdc) - 项目结构、技术架构详情
- [AI更新指导](mdc:.cursor/rules/ai-updata-guide.mdc) - AI助手更新机制详细说明
